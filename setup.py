from setuptools import setup, find_packages

setup(
    name="pytest-plugin-conflict-demo",
    version="0.1.0",
    description="A demo project showing pytest plugin conflicts and their resolution",
    author="Demo Author",
    author_email="<EMAIL>",
    packages=find_packages(),
    install_requires=[
        "pytest>=6.0.0",
    ],
    entry_points={
        "pytest11": [
            "plugin_a = pytest_plugins.plugin_a",
            "plugin_b = pytest_plugins.plugin_b",
        ]
    },
    classifiers=[
        "Framework :: Pytest",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.6",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
    ],
)