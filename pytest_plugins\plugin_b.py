"""
Plugin B - 部署环境插件
也提供 --env 参数来指定部署环境（与 Plugin A 冲突）
"""
import pytest


def pytest_addoption(parser):
    """添加命令行选项 - 与Plugin A冲突"""
    group = parser.getgroup("plugin_b", "Plugin B Options")
    group.addoption(
        "--env",
        action="store",
        default="staging",
        help="Specify the deployment environment (staging, production, local). Plugin B version."
    )


@pytest.fixture(scope="session")
def environment_b(request):
    """获取环境配置 - Plugin B 版本"""
    env = request.config.getoption("--env")
    print(f"\n[Plugin B] Deploying to environment: {env}")
    return {
        "name": env,
        "plugin": "plugin_b",
        "config": {
            "deployment_url": f"https://{env}-deploy-b.example.com",
            "container_registry": f"registry.{env}.example.com",
            "replicas": 3 if env == "production" else 1
        }
    }


@pytest.fixture
def deployment_config_b(environment_b):
    """部署配置 - Plugin B 版本"""
    return environment_b["config"]["deployment_url"]


@pytest.fixture
def container_config_b(environment_b):
    """容器配置 - Plugin B 版本"""
    return environment_b["config"]["container_registry"]


def pytest_configure(config):
    """配置插件"""
    print(f"\n[Plugin B] Configured with env: {config.getoption('--env')}")


def pytest_collection_modifyitems(config, items):
    """修改测试项目收集"""
    env = config.getoption("--env")
    print(f"\n[Plugin B] Processing {len(items)} test items for deployment environment: {env}")
    
    # 为每个测试项添加标记
    for item in items:
        item.add_marker(pytest.mark.env_b(env))