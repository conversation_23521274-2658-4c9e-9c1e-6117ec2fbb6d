"""
测试用例演示插件功能
"""
import pytest


class TestPluginA:
    """测试 Plugin A 的功能"""
    
    def test_environment_a_fixture(self, environment_a):
        """测试环境配置 fixture - Plugin A"""
        print(f"\n测试环境配置: {environment_a}")
        assert environment_a["plugin"] == "plugin_a"
        assert "name" in environment_a
        assert "config" in environment_a
    
    def test_database_config_a(self, database_config_a):
        """测试数据库配置 - Plugin A"""
        print(f"\n数据库配置: {database_config_a}")
        assert "postgresql://" in database_config_a
        assert "_db_a" in database_config_a
    
    def test_api_config_a(self, api_config_a):
        """测试API配置 - Plugin A"""
        print(f"\nAPI配置: {api_config_a}")
        assert "https://" in api_config_a
        assert "-api-a.example.com" in api_config_a


class TestPluginB:
    """测试 Plugin B 的功能"""
    
    def test_environment_b_fixture(self, environment_b):
        """测试环境配置 fixture - Plugin B"""
        print(f"\n部署环境配置: {environment_b}")
        assert environment_b["plugin"] == "plugin_b"
        assert "name" in environment_b
        assert "config" in environment_b
    
    def test_deployment_config_b(self, deployment_config_b):
        """测试部署配置 - Plugin B"""
        print(f"\n部署配置: {deployment_config_b}")
        assert "https://" in deployment_config_b
        assert "-deploy-b.example.com" in deployment_config_b
    
    def test_container_config_b(self, container_config_b):
        """测试容器配置 - Plugin B"""
        print(f"\n容器配置: {container_config_b}")
        assert "registry." in container_config_b
        assert ".example.com" in container_config_b


class TestCombinedFeatures:
    """测试组合功能（当两个插件同时使用时）"""
    
    def test_both_plugins_available(self, environment_a, environment_b):
        """测试两个插件都可用时的情况"""
        print(f"\nPlugin A 环境: {environment_a['name']}")
        print(f"Plugin B 环境: {environment_b['name']}")
        
        assert environment_a["plugin"] == "plugin_a"
        assert environment_b["plugin"] == "plugin_b"
    
    def test_environment_consistency(self, environment_a, environment_b):
        """测试环境一致性"""
        print(f"\nPlugin A 认为环境是: {environment_a['name']}")
        print(f"Plugin B 认为环境是: {environment_b['name']}")
        
        # 这里可能会出现不一致的情况
        # 因为两个插件都定义了 --env 参数