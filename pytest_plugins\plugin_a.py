"""
Plugin A - 环境配置插件
提供 --env 参数来指定运行环境
"""
import pytest


def pytest_addoption(parser):
    """添加命令行选项"""
    group = parser.getgroup("plugin_a", "Plugin A Options")
    group.addoption(
        "--env",
        action="store",
        default="dev",
        help="Specify the environment to run tests against (dev, test, prod). Plugin A version."
    )


@pytest.fixture(scope="session")
def environment_a(request):
    """获取环境配置 - Plugin A 版本"""
    env = request.config.getoption("--env")
    print(f"\n[Plugin A] Running tests in environment: {env}")
    return {
        "name": env,
        "plugin": "plugin_a",
        "config": {
            "database_url": f"postgresql://localhost/{env}_db_a",
            "api_base_url": f"https://{env}-api-a.example.com",
            "debug_mode": env == "dev"
        }
    }


@pytest.fixture
def database_config_a(environment_a):
    """数据库配置 - Plugin A 版本"""
    return environment_a["config"]["database_url"]


@pytest.fixture
def api_config_a(environment_a):
    """API配置 - Plugin A 版本"""
    return environment_a["config"]["api_base_url"]


def pytest_configure(config):
    """配置插件"""
    print(f"\n[Plugin A] Configured with env: {config.getoption('--env')}")


def pytest_collection_modifyitems(config, items):
    """修改测试项目收集"""
    env = config.getoption("--env")
    print(f"\n[Plugin A] Collected {len(items)} test items for environment: {env}")
    
    # 为每个测试项添加标记
    for item in items:
        item.add_marker(pytest.mark.env_a(env))